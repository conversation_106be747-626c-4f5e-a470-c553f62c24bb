-- Migration: Add form_url tracking to form submissions
-- This migration adds form_url field to track which page/route the form was submitted from

-- Add form_url column to contact_form_submissions table
ALTER TABLE contact_form_submissions 
ADD COLUMN IF NOT EXISTS form_url TEXT;

-- Add form_url column to event_form_submissions table  
ALTER TABLE event_form_submissions 
ADD COLUMN IF NOT EXISTS form_url TEXT;

-- Add comment to document the new field
COMMENT ON COLUMN contact_form_submissions.form_url IS 'URL/route from which the form was submitted for tracking purposes';
COMMENT ON COLUMN event_form_submissions.form_url IS 'URL/route from which the form was submitted for tracking purposes';

-- Create index for better query performance on form_url
CREATE INDEX IF NOT EXISTS idx_contact_form_submissions_form_url ON contact_form_submissions(form_url);
CREATE INDEX IF NOT EXISTS idx_event_form_submissions_form_url ON event_form_submissions(form_url);

-- Update existing submissions to have a default form_url if needed (optional)
-- This helps identify which submissions were made before this tracking was implemented
UPDATE contact_form_submissions 
SET form_url = 'legacy-submission' 
WHERE form_url IS NULL;

UPDATE event_form_submissions 
SET form_url = 'legacy-submission' 
WHERE form_url IS NULL;

-- Ensure the contact-attachments bucket exists with proper configuration
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'contact-attachments',
    'contact-attachments',
    false, -- Keep private for security
    10485760, -- 10MB limit
    ARRAY[
        'application/pdf', 
        'application/msword', 
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel',
        'image/jpeg', 
        'image/png', 
        'image/jpg',
        'image/gif',
        'image/webp',
        'text/plain',
        'text/csv'
    ]
) ON CONFLICT (id) DO UPDATE SET
    file_size_limit = EXCLUDED.file_size_limit,
    allowed_mime_types = EXCLUDED.allowed_mime_types;

-- Create event-attachments bucket if it doesn't exist
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'event-attachments',
    'event-attachments', 
    false, -- Keep private for security
    10485760, -- 10MB limit
    ARRAY[
        'application/pdf', 
        'application/msword', 
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel',
        'image/jpeg', 
        'image/png', 
        'image/jpg',
        'image/gif',
        'image/webp',
        'text/plain',
        'text/csv'
    ]
) ON CONFLICT (id) DO NOTHING;

-- Storage policies for contact-attachments bucket (admin only access)
-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Admin can view contact attachments" ON storage.objects;
DROP POLICY IF EXISTS "Admin can upload contact attachments" ON storage.objects;
DROP POLICY IF EXISTS "Admin can delete contact attachments" ON storage.objects;

-- Create new policies for contact-attachments
CREATE POLICY "Admin can view contact attachments" ON storage.objects
FOR SELECT USING (
    bucket_id = 'contact-attachments' AND 
    auth.role() = 'authenticated'
);

CREATE POLICY "Admin can upload contact attachments" ON storage.objects
FOR INSERT WITH CHECK (
    bucket_id = 'contact-attachments' AND 
    auth.role() = 'authenticated'
);

CREATE POLICY "Admin can delete contact attachments" ON storage.objects
FOR DELETE USING (
    bucket_id = 'contact-attachments' AND 
    auth.role() = 'authenticated'
);

-- Storage policies for event-attachments bucket (admin only access)
-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Admin can view event attachments" ON storage.objects;
DROP POLICY IF EXISTS "Admin can upload event attachments" ON storage.objects;
DROP POLICY IF EXISTS "Admin can delete event attachments" ON storage.objects;

-- Create new policies for event-attachments
CREATE POLICY "Admin can view event attachments" ON storage.objects
FOR SELECT USING (
    bucket_id = 'event-attachments' AND 
    auth.role() = 'authenticated'
);

CREATE POLICY "Admin can upload event attachments" ON storage.objects
FOR INSERT WITH CHECK (
    bucket_id = 'event-attachments' AND 
    auth.role() = 'authenticated'
);

CREATE POLICY "Admin can delete event attachments" ON storage.objects
FOR DELETE USING (
    bucket_id = 'event-attachments' AND 
    auth.role() = 'authenticated'
);

-- Allow public to upload files (for form submissions)
-- This is needed for the contact page service to upload files
CREATE POLICY "Public can upload contact attachments" ON storage.objects
FOR INSERT WITH CHECK (
    bucket_id = 'contact-attachments'
);

CREATE POLICY "Public can upload event attachments" ON storage.objects
FOR INSERT WITH CHECK (
    bucket_id = 'event-attachments'
);

-- Add attachment_type column to event_form_submissions if it doesn't exist
ALTER TABLE event_form_submissions 
ADD COLUMN IF NOT EXISTS attachment_type TEXT;

-- Add comment for the attachment_type field
COMMENT ON COLUMN event_form_submissions.attachment_type IS 'MIME type of the uploaded attachment file';

-- Create a view for admin to easily see form submission statistics by URL
CREATE OR REPLACE VIEW form_submissions_by_url AS
SELECT 
    form_url,
    'contact' as form_type,
    COUNT(*) as total_submissions,
    COUNT(*) FILTER (WHERE status = 'new') as new_submissions,
    COUNT(*) FILTER (WHERE status = 'read') as read_submissions,
    COUNT(*) FILTER (WHERE status = 'replied') as replied_submissions,
    COUNT(*) FILTER (WHERE is_spam = true) as spam_submissions,
    COUNT(*) FILTER (WHERE attachment_url IS NOT NULL) as submissions_with_attachments,
    MIN(created_at) as first_submission,
    MAX(created_at) as latest_submission
FROM contact_form_submissions 
WHERE form_url IS NOT NULL
GROUP BY form_url

UNION ALL

SELECT 
    form_url,
    'event' as form_type,
    COUNT(*) as total_submissions,
    COUNT(*) FILTER (WHERE status = 'new') as new_submissions,
    COUNT(*) FILTER (WHERE status = 'read') as read_submissions,
    COUNT(*) FILTER (WHERE status = 'replied') as replied_submissions,
    COUNT(*) FILTER (WHERE is_spam = true) as spam_submissions,
    COUNT(*) FILTER (WHERE attachment_url IS NOT NULL) as submissions_with_attachments,
    MIN(created_at) as first_submission,
    MAX(created_at) as latest_submission
FROM event_form_submissions 
WHERE form_url IS NOT NULL
GROUP BY form_url;

-- Grant access to the view for authenticated users
GRANT SELECT ON form_submissions_by_url TO authenticated;
