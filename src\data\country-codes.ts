export interface CountryCode {
    code: string;
    name: string;
    flag: string;
    iso2: string; // ISO 3166-1 alpha-2 country code for API matching
}

export const countryCodes: CountryCode[] = [
    // A
    { code: "+93", name: "Afghanistan", flag: "🇦🇫", iso2: "AF" },
    { code: "+355", name: "Albania", flag: "🇦🇱", iso2: "AL" },
    { code: "+213", name: "Algeria", flag: "🇩🇿", iso2: "DZ" },
    { code: "+1684", name: "American Samoa", flag: "🇦🇸", iso2: "AS" },
    { code: "+376", name: "Andorra", flag: "🇦🇩", iso2: "AD" },
    { code: "+244", name: "Angola", flag: "🇦🇴", iso2: "AO" },
    { code: "+1264", name: "<PERSON><PERSON><PERSON>", flag: "🇦🇮", iso2: "AI" },
    { code: "+1268", name: "Antigua and Barbuda", flag: "🇦🇬", iso2: "AG" },
    { code: "+54", name: "Argentina", flag: "🇦🇷", iso2: "AR" },
    { code: "+374", name: "Armenia", flag: "🇦🇲", iso2: "AM" },
    { code: "+297", name: "Aruba", flag: "🇦🇼", iso2: "AW" },
    { code: "+61", name: "Australia", flag: "🇦🇺", iso2: "AU" },
    { code: "+43", name: "Austria", flag: "🇦🇹", iso2: "AT" },
    { code: "+994", name: "Azerbaijan", flag: "🇦🇿", iso2: "AZ" },

    // B
    { code: "+1242", name: "Bahamas", flag: "🇧🇸", iso2: "BS" },
    { code: "+973", name: "Bahrain", flag: "🇧🇭", iso2: "BH" },
    { code: "+880", name: "Bangladesh", flag: "🇧🇩", iso2: "BD" },
    { code: "+1246", name: "Barbados", flag: "🇧🇧", iso2: "BB" },
    { code: "+375", name: "Belarus", flag: "🇧🇾", iso2: "BY" },
    { code: "+32", name: "Belgium", flag: "🇧🇪", iso2: "BE" },
    { code: "+501", name: "Belize", flag: "🇧🇿", iso2: "BZ" },
    { code: "+229", name: "Benin", flag: "🇧🇯", iso2: "BJ" },
    { code: "+1441", name: "Bermuda", flag: "🇧🇲", iso2: "BM" },
    { code: "+975", name: "Bhutan", flag: "🇧🇹", iso2: "BT" },
    { code: "+591", name: "Bolivia", flag: "🇧🇴", iso2: "BO" },
    { code: "+387", name: "Bosnia and Herzegovina", flag: "🇧🇦", iso2: "BA" },
    { code: "+267", name: "Botswana", flag: "🇧🇼", iso2: "BW" },
    { code: "+55", name: "Brazil", flag: "🇧🇷", iso2: "BR" },
    { code: "+673", name: "Brunei", flag: "🇧🇳", iso2: "BN" },
    { code: "+359", name: "Bulgaria", flag: "🇧🇬", iso2: "BG" },
    { code: "+226", name: "Burkina Faso", flag: "🇧🇫", iso2: "BF" },
    { code: "+257", name: "Burundi", flag: "🇧🇮", iso2: "BI" },

    // C
    { code: "+855", name: "Cambodia", flag: "🇰🇭", iso2: "KH" },
    { code: "+237", name: "Cameroon", flag: "🇨🇲", iso2: "CM" },
    { code: "+1", name: "Canada", flag: "🇨🇦", iso2: "CA" },
    { code: "+238", name: "Cape Verde", flag: "🇨🇻", iso2: "CV" },
    { code: "+1345", name: "Cayman Islands", flag: "🇰🇾", iso2: "KY" },
    { code: "+236", name: "Central African Republic", flag: "🇨🇫", iso2: "CF" },
    { code: "+235", name: "Chad", flag: "🇹🇩", iso2: "TD" },
    { code: "+56", name: "Chile", flag: "🇨🇱", iso2: "CL" },
    { code: "+86", name: "China", flag: "🇨🇳", iso2: "CN" },
    { code: "+57", name: "Colombia", flag: "🇨🇴", iso2: "CO" },
    { code: "+269", name: "Comoros", flag: "🇰🇲", iso2: "KM" },
    { code: "+242", name: "Congo", flag: "🇨🇬", iso2: "CG" },
    { code: "+243", name: "Congo (DRC)", flag: "🇨🇩", iso2: "CD" },
    { code: "+682", name: "Cook Islands", flag: "🇨🇰", iso2: "CK" },
    { code: "+506", name: "Costa Rica", flag: "🇨🇷", iso2: "CR" },
    { code: "+225", name: "Côte d'Ivoire", flag: "🇨🇮", iso2: "CI" },
    { code: "+385", name: "Croatia", flag: "🇭🇷", iso2: "HR" },
    { code: "+53", name: "Cuba", flag: "🇨🇺", iso2: "CU" },
    { code: "+357", name: "Cyprus", flag: "🇨🇾", iso2: "CY" },
    { code: "+420", name: "Czech Republic", flag: "🇨🇿", iso2: "CZ" },

    // D
    { code: "+45", name: "Denmark", flag: "🇩🇰", iso2: "DK" },
    { code: "+253", name: "Djibouti", flag: "🇩🇯", iso2: "DJ" },
    { code: "+1767", name: "Dominica", flag: "🇩🇲", iso2: "DM" },
    { code: "+1809", name: "Dominican Republic", flag: "🇩🇴", iso2: "DO" },

    // E
    { code: "+593", name: "Ecuador", flag: "🇪🇨", iso2: "EC" },
    { code: "+20", name: "Egypt", flag: "🇪🇬", iso2: "EG" },
    { code: "+503", name: "El Salvador", flag: "🇸🇻", iso2: "SV" },
    { code: "+240", name: "Equatorial Guinea", flag: "🇬🇶", iso2: "GQ" },
    { code: "+291", name: "Eritrea", flag: "🇪🇷", iso2: "ER" },
    { code: "+372", name: "Estonia", flag: "🇪🇪", iso2: "EE" },
    { code: "+251", name: "Ethiopia", flag: "🇪🇹", iso2: "ET" },

    // F
    { code: "+679", name: "Fiji", flag: "🇫🇯", iso2: "FJ" },
    { code: "+358", name: "Finland", flag: "🇫🇮", iso2: "FI" },
    { code: "+33", name: "France", flag: "🇫🇷", iso2: "FR" },
    { code: "+594", name: "French Guiana", flag: "🇬🇫", iso2: "GF" },
    { code: "+689", name: "French Polynesia", flag: "🇵🇫", iso2: "PF" },

    // G
    { code: "+241", name: "Gabon", flag: "🇬🇦", iso2: "GA" },
    { code: "+220", name: "Gambia", flag: "🇬🇲", iso2: "GM" },
    { code: "+995", name: "Georgia", flag: "🇬🇪", iso2: "GE" },
    { code: "+49", name: "Germany", flag: "🇩🇪", iso2: "DE" },
    { code: "+233", name: "Ghana", flag: "🇬🇭", iso2: "GH" },
    { code: "+350", name: "Gibraltar", flag: "🇬🇮", iso2: "GI" },
    { code: "+30", name: "Greece", flag: "🇬🇷", iso2: "GR" },
    { code: "+299", name: "Greenland", flag: "🇬🇱", iso2: "GL" },
    { code: "+1473", name: "Grenada", flag: "🇬🇩", iso2: "GD" },
    { code: "+590", name: "Guadeloupe", flag: "🇬🇵", iso2: "GP" },
    { code: "+1671", name: "Guam", flag: "🇬🇺", iso2: "GU" },
    { code: "+502", name: "Guatemala", flag: "🇬🇹", iso2: "GT" },
    { code: "+224", name: "Guinea", flag: "🇬🇳", iso2: "GN" },
    { code: "+245", name: "Guinea-Bissau", flag: "🇬🇼", iso2: "GW" },
    { code: "+592", name: "Guyana", flag: "🇬🇾", iso2: "GY" },

    // H
    { code: "+509", name: "Haiti", flag: "🇭🇹", iso2: "HT" },
    { code: "+504", name: "Honduras", flag: "🇭🇳", iso2: "HN" },
    { code: "+852", name: "Hong Kong", flag: "🇭🇰", iso2: "HK" },
    { code: "+36", name: "Hungary", flag: "🇭🇺", iso2: "HU" },

    // I
    { code: "+354", name: "Iceland", flag: "🇮🇸", iso2: "IS" },
    { code: "+91", name: "India", flag: "🇮🇳", iso2: "IN" },
    { code: "+62", name: "Indonesia", flag: "🇮🇩", iso2: "ID" },
    { code: "+98", name: "Iran", flag: "🇮🇷", iso2: "IR" },
    { code: "+964", name: "Iraq", flag: "🇮🇶", iso2: "IQ" },
    { code: "+353", name: "Ireland", flag: "🇮🇪", iso2: "IE" },
    { code: "+972", name: "Israel", flag: "🇮🇱", iso2: "IL" },
    { code: "+39", name: "Italy", flag: "🇮🇹", iso2: "IT" },

    // J
    { code: "+1876", name: "Jamaica", flag: "🇯🇲", iso2: "JM" },
    { code: "+81", name: "Japan", flag: "🇯🇵", iso2: "JP" },
    { code: "+962", name: "Jordan", flag: "🇯🇴", iso2: "JO" },

    // K
    { code: "+7", name: "Kazakhstan", flag: "🇰🇿", iso2: "KZ" },
    { code: "+254", name: "Kenya", flag: "🇰🇪", iso2: "KE" },
    { code: "+686", name: "Kiribati", flag: "🇰🇮", iso2: "KI" },
    { code: "+965", name: "Kuwait", flag: "🇰🇼", iso2: "KW" },
    { code: "+996", name: "Kyrgyzstan", flag: "🇰🇬", iso2: "KG" },

    // L
    { code: "+856", name: "Laos", flag: "🇱🇦", iso2: "LA" },
    { code: "+371", name: "Latvia", flag: "🇱🇻", iso2: "LV" },
    { code: "+961", name: "Lebanon", flag: "🇱🇧", iso2: "LB" },
    { code: "+266", name: "Lesotho", flag: "🇱🇸", iso2: "LS" },
    { code: "+231", name: "Liberia", flag: "🇱🇷", iso2: "LR" },
    { code: "+218", name: "Libya", flag: "🇱🇾", iso2: "LY" },
    { code: "+423", name: "Liechtenstein", flag: "🇱🇮", iso2: "LI" },
    { code: "+370", name: "Lithuania", flag: "🇱🇹", iso2: "LT" },
    { code: "+352", name: "Luxembourg", flag: "🇱🇺", iso2: "LU" },

    // M
    { code: "+853", name: "Macau", flag: "🇲🇴", iso2: "MO" },
    { code: "+389", name: "North Macedonia", flag: "🇲🇰", iso2: "MK" },
    { code: "+261", name: "Madagascar", flag: "🇲🇬", iso2: "MG" },
    { code: "+265", name: "Malawi", flag: "🇲🇼", iso2: "MW" },
    { code: "+60", name: "Malaysia", flag: "🇲🇾", iso2: "MY" },
    { code: "+960", name: "Maldives", flag: "🇲🇻", iso2: "MV" },
    { code: "+223", name: "Mali", flag: "🇲🇱", iso2: "ML" },
    { code: "+356", name: "Malta", flag: "🇲🇹", iso2: "MT" },
    { code: "+692", name: "Marshall Islands", flag: "🇲🇭", iso2: "MH" },
    { code: "+596", name: "Martinique", flag: "🇲🇶", iso2: "MQ" },
    { code: "+222", name: "Mauritania", flag: "🇲🇷", iso2: "MR" },
    { code: "+230", name: "Mauritius", flag: "🇲🇺", iso2: "MU" },
    { code: "+52", name: "Mexico", flag: "🇲🇽", iso2: "MX" },
    { code: "+691", name: "Micronesia", flag: "🇫🇲", iso2: "FM" },
    { code: "+373", name: "Moldova", flag: "🇲🇩", iso2: "MD" },
    { code: "+377", name: "Monaco", flag: "🇲🇨", iso2: "MC" },
    { code: "+976", name: "Mongolia", flag: "🇲🇳", iso2: "MN" },
    { code: "+382", name: "Montenegro", flag: "🇲🇪", iso2: "ME" },
    { code: "+1664", name: "Montserrat", flag: "🇲🇸", iso2: "MS" },
    { code: "+212", name: "Morocco", flag: "🇲🇦", iso2: "MA" },
    { code: "+258", name: "Mozambique", flag: "🇲🇿", iso2: "MZ" },
    { code: "+95", name: "Myanmar", flag: "🇲🇲", iso2: "MM" },

    // N
    { code: "+264", name: "Namibia", flag: "🇳🇦", iso2: "NA" },
    { code: "+674", name: "Nauru", flag: "🇳🇷", iso2: "NR" },
    { code: "+977", name: "Nepal", flag: "🇳🇵", iso2: "NP" },
    { code: "+31", name: "Netherlands", flag: "🇳🇱", iso2: "NL" },
    { code: "+687", name: "New Caledonia", flag: "🇳🇨", iso2: "NC" },
    { code: "+64", name: "New Zealand", flag: "🇳🇿", iso2: "NZ" },
    { code: "+505", name: "Nicaragua", flag: "🇳🇮", iso2: "NI" },
    { code: "+227", name: "Niger", flag: "🇳🇪", iso2: "NE" },
    { code: "+234", name: "Nigeria", flag: "🇳🇬", iso2: "NG" },
    { code: "+683", name: "Niue", flag: "🇳🇺", iso2: "NU" },
    { code: "+850", name: "North Korea", flag: "🇰🇵", iso2: "KP" },
    { code: "+47", name: "Norway", flag: "🇳🇴", iso2: "NO" },

    // O
    { code: "+968", name: "Oman", flag: "🇴🇲", iso2: "OM" },

    // P
    { code: "+92", name: "Pakistan", flag: "🇵🇰", iso2: "PK" },
    { code: "+680", name: "Palau", flag: "🇵🇼", iso2: "PW" },
    { code: "+970", name: "Palestine", flag: "🇵🇸", iso2: "PS" },
    { code: "+507", name: "Panama", flag: "🇵🇦", iso2: "PA" },
    { code: "+675", name: "Papua New Guinea", flag: "🇵🇬", iso2: "PG" },
    { code: "+595", name: "Paraguay", flag: "🇵🇾", iso2: "PY" },
    { code: "+51", name: "Peru", flag: "🇵🇪", iso2: "PE" },
    { code: "+63", name: "Philippines", flag: "🇵🇭", iso2: "PH" },
    { code: "+48", name: "Poland", flag: "🇵🇱", iso2: "PL" },
    { code: "+351", name: "Portugal", flag: "🇵🇹", iso2: "PT" },
    { code: "+1787", name: "Puerto Rico", flag: "🇵🇷", iso2: "PR" },

    // Q
    { code: "+974", name: "Qatar", flag: "🇶🇦", iso2: "QA" },

    // R
    { code: "+262", name: "Réunion", flag: "🇷🇪", iso2: "RE" },
    { code: "+40", name: "Romania", flag: "🇷🇴", iso2: "RO" },
    { code: "+7", name: "Russia", flag: "🇷🇺", iso2: "RU" },
    { code: "+250", name: "Rwanda", flag: "🇷🇼", iso2: "RW" },

    // S
    { code: "+1869", name: "Saint Kitts and Nevis", flag: "🇰🇳", iso2: "KN" },
    { code: "+1758", name: "Saint Lucia", flag: "🇱🇨", iso2: "LC" },
    { code: "+1784", name: "Saint Vincent and the Grenadines", flag: "🇻🇨", iso2: "VC" },
    { code: "+685", name: "Samoa", flag: "🇼🇸", iso2: "WS" },
    { code: "+378", name: "San Marino", flag: "🇸🇲", iso2: "SM" },
    { code: "+239", name: "São Tomé and Príncipe", flag: "🇸🇹", iso2: "ST" },
    { code: "+966", name: "Saudi Arabia", flag: "🇸🇦", iso2: "SA" },
    { code: "+221", name: "Senegal", flag: "🇸🇳", iso2: "SN" },
    { code: "+381", name: "Serbia", flag: "🇷🇸", iso2: "RS" },
    { code: "+248", name: "Seychelles", flag: "🇸🇨", iso2: "SC" },
    { code: "+232", name: "Sierra Leone", flag: "🇸🇱", iso2: "SL" },
    { code: "+65", name: "Singapore", flag: "🇸🇬", iso2: "SG" },
    { code: "+421", name: "Slovakia", flag: "🇸🇰", iso2: "SK" },
    { code: "+386", name: "Slovenia", flag: "🇸🇮", iso2: "SI" },
    { code: "+677", name: "Solomon Islands", flag: "🇸🇧", iso2: "SB" },
    { code: "+252", name: "Somalia", flag: "🇸🇴", iso2: "SO" },
    { code: "+27", name: "South Africa", flag: "🇿🇦", iso2: "ZA" },
    { code: "+82", name: "South Korea", flag: "🇰🇷", iso2: "KR" },
    { code: "+211", name: "South Sudan", flag: "🇸🇸", iso2: "SS" },
    { code: "+34", name: "Spain", flag: "🇪🇸", iso2: "ES" },
    { code: "+94", name: "Sri Lanka", flag: "🇱🇰", iso2: "LK" },
    { code: "+249", name: "Sudan", flag: "🇸🇩", iso2: "SD" },
    { code: "+597", name: "Suriname", flag: "🇸🇷", iso2: "SR" },
    { code: "+268", name: "Eswatini", flag: "🇸🇿", iso2: "SZ" },
    { code: "+46", name: "Sweden", flag: "🇸🇪", iso2: "SE" },
    { code: "+41", name: "Switzerland", flag: "🇨🇭", iso2: "CH" },
    { code: "+963", name: "Syria", flag: "🇸🇾", iso2: "SY" },

    // T
    { code: "+886", name: "Taiwan", flag: "🇹🇼", iso2: "TW" },
    { code: "+992", name: "Tajikistan", flag: "🇹🇯", iso2: "TJ" },
    { code: "+255", name: "Tanzania", flag: "🇹🇿", iso2: "TZ" },
    { code: "+66", name: "Thailand", flag: "🇹🇭", iso2: "TH" },
    { code: "+670", name: "Timor-Leste", flag: "🇹🇱", iso2: "TL" },
    { code: "+228", name: "Togo", flag: "🇹🇬", iso2: "TG" },
    { code: "+676", name: "Tonga", flag: "🇹🇴", iso2: "TO" },
    { code: "+1868", name: "Trinidad and Tobago", flag: "🇹🇹", iso2: "TT" },
    { code: "+216", name: "Tunisia", flag: "🇹🇳", iso2: "TN" },
    { code: "+90", name: "Turkey", flag: "🇹🇷", iso2: "TR" },
    { code: "+993", name: "Turkmenistan", flag: "🇹🇲", iso2: "TM" },
    { code: "+1649", name: "Turks and Caicos Islands", flag: "🇹🇨", iso2: "TC" },
    { code: "+688", name: "Tuvalu", flag: "🇹🇻", iso2: "TV" },

    // U
    { code: "+256", name: "Uganda", flag: "🇺🇬", iso2: "UG" },
    { code: "+380", name: "Ukraine", flag: "🇺🇦", iso2: "UA" },
    { code: "+971", name: "United Arab Emirates", flag: "🇦🇪", iso2: "AE" },
    { code: "+44", name: "United Kingdom", flag: "🇬🇧", iso2: "GB" },
    { code: "+1", name: "United States", flag: "🇺🇸", iso2: "US" },
    { code: "+598", name: "Uruguay", flag: "🇺🇾", iso2: "UY" },
    { code: "+998", name: "Uzbekistan", flag: "🇺🇿", iso2: "UZ" },

    // V
    { code: "+678", name: "Vanuatu", flag: "🇻🇺", iso2: "VU" },
    { code: "+379", name: "Vatican City", flag: "🇻🇦", iso2: "VA" },
    { code: "+58", name: "Venezuela", flag: "🇻🇪", iso2: "VE" },
    { code: "+84", name: "Vietnam", flag: "🇻🇳", iso2: "VN" },
    { code: "+1284", name: "British Virgin Islands", flag: "🇻🇬", iso2: "VG" },
    { code: "+1340", name: "US Virgin Islands", flag: "🇻🇮", iso2: "VI" },

    // W
    { code: "+681", name: "Wallis and Futuna", flag: "🇼🇫", iso2: "WF" },

    // Y
    { code: "+967", name: "Yemen", flag: "🇾🇪", iso2: "YE" },

    // Z
    { code: "+260", name: "Zambia", flag: "🇿🇲", iso2: "ZM" },
    { code: "+263", name: "Zimbabwe", flag: "🇿🇼", iso2: "ZW" }
];

// Default country code (UAE)
export const defaultCountryCode = "+971";

// Country detection API interface
export interface CountryDetectionResponse {
    country: string; // ISO 3166-1 alpha-2 country code
    ip: string;
}

// Function to get country code by ISO2 code
export const getCountryCodeByISO2 = (iso2: string): CountryCode | null => {
    return countryCodes.find(country => country.iso2.toLowerCase() === iso2.toLowerCase()) || null;
};

// Function to detect user's country and return appropriate country code
export const detectUserCountry = async (): Promise<string> => {
    try {
        const response = await fetch('https://api.country.is/', {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
            },
        });

        if (!response.ok) {
            throw new Error('Failed to fetch country data');
        }

        const data: CountryDetectionResponse = await response.json();
        const detectedCountry = getCountryCodeByISO2(data.country);

        return detectedCountry?.code || defaultCountryCode;
    } catch (error) {
        console.warn('Failed to detect user country:', error);
        return defaultCountryCode;
    }
};